import { useEffect, useMemo, useRef, useState } from "react";
import {
  GoogleMap,
  Marker,
  useLoadScript,
  Autocomplete,
} from "@react-google-maps/api";

const libraries: ("places")[] = ["places"]; // cần cho Autocomplete

type LatLng = {
  lat: number;
  lng: number;
};

export default function App() {
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY as string;
  const [center, setCenter] = useState<LatLng>({
    lat: 10.776889,
    lng: 106.700806,
  }); // HCM
  const [marker, setMarker] = useState<LatLng>(center);
  const [status, setStatus] = useState<string>("Chưa tải");
  const [errorMsg, setErrorMsg] = useState<string>("");
  const inputRef = useRef<HTMLInputElement | null>(null);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);

  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: apiKey,
    libraries,
  });

  useEffect(() => {
    if (loadError) {
      setStatus("Lỗi tải SDK");
      setErrorMsg(loadError.message || "Không rõ nguyên nhân");
    }
  }, [loadError]);

  useEffect(() => {
    if (isLoaded) {
      setStatus("SDK đã tải – KEY hoạt động ✅");
    }
  }, [isLoaded]);

  const mapContainerStyle = useMemo(
    () => ({
      width: "100%",
      height: "70vh",
      borderRadius: "12px",
      border: "1px solid #e5e7eb",
    }),
    []
  );

  const handlePlaceChanged = () => {
    const place = autocompleteRef.current?.getPlace();
    if (!place || !place.geometry || !place.geometry.location) {
      setStatus("Không lấy được vị trí từ Autocomplete ❌");
      return;
    }
    const pos = {
      lat: place.geometry.location.lat(),
      lng: place.geometry.location.lng(),
    };
    setCenter(pos);
    setMarker(pos);
    setStatus(`Đã chọn: ${place.formatted_address || place.name} ✅`);
  };

  const geocodeAddress = async () => {
    const addr = inputRef.current?.value?.trim();
    if (!addr) {
      setStatus("Nhập địa chỉ để geocode");
      return;
    }
    setStatus("Đang geocode…");
    try {
      const url = new URL("https://maps.googleapis.com/maps/api/geocode/json");
      url.searchParams.set("address", addr);
      url.searchParams.set("key", apiKey);

      const res = await fetch(url.toString());
      const json = await res.json();

      if (json.status !== "OK" || !json.results?.length) {
        setStatus(`Geocode thất bại: ${json.status} ❌`);
        setErrorMsg(JSON.stringify(json, null, 2));
        return;
      }

      const loc = json.results[0].geometry.location;
      const pos = { lat: loc.lat, lng: loc.lng };
      setCenter(pos);
      setMarker(pos);
      setStatus(`Geocode OK: ${json.results[0].formatted_address} ✅`);
      setErrorMsg("");
    } catch (e) {
      setStatus("Geocode lỗi ❌");
      setErrorMsg(String(e));
    }
  };

  if (!apiKey) {
    return (
      <div style={{ padding: 24, fontFamily: "system-ui" }}>
        <h2>Thiếu API Key</h2>
        <p>
          Hãy tạo file <code>.env</code> và đặt biến{" "}
          <code>VITE_GOOGLE_MAPS_API_KEY</code>.
        </p>
      </div>
    );
  }

  return (
    <div
      style={{
        padding: 24,
        fontFamily: "system-ui",
        maxWidth: 1000,
        margin: "0 auto",
      }}
    >
      <h1 style={{ marginBottom: 8 }}>Google Maps Key Tester</h1>
      <p style={{ marginTop: 0, color: "#6b7280" }}>
        Kiểm tra: load bản đồ (Maps JS), tìm địa điểm (Places Autocomplete), geocode địa chỉ (Geocoding).
      </p>

      <div style={{ display: "grid", gap: 12, marginBottom: 12 }}>
        <div>
          <label style={{ display: "block", marginBottom: 6, fontWeight: 600 }}>
            Tìm nơi (Autocomplete – Places API)
          </label>
          {isLoaded && (
            <Autocomplete
              onLoad={(ac) => (autocompleteRef.current = ac)}
              onPlaceChanged={handlePlaceChanged}
            >
              <input
                type="text"
                placeholder="Ví dụ: Landmark 81, Saigon Zoo…"
                style={{
                  width: "100%",
                  padding: "10px 12px",
                  borderRadius: 8,
                  border: "1px solid #d1d5db",
                  outline: "none",
                }}
                ref={inputRef}
              />
            </Autocomplete>
          )}
        </div>

        <div style={{ display: "flex", gap: 8 }}>
          <button
            onClick={geocodeAddress}
            style={{
              padding: "10px 14px",
              borderRadius: 8,
              border: "1px solid #2563eb",
              background: "#2563eb",
              color: "white",
              cursor: "pointer",
            }}
          >
            Geocode địa chỉ (REST)
          </button>
          <button
            onClick={() => {
              const hcm = { lat: 10.776889, lng: 106.700806 };
              setCenter(hcm);
              setMarker(hcm);
              setStatus("Reset về HCM");
            }}
            style={{
              padding: "10px 14px",
              borderRadius: 8,
              border: "1px solid #e5e7eb",
              background: "white",
              cursor: "pointer",
            }}
          >
            Reset về HCM
          </button>
        </div>

        <div
          style={{
            padding: 12,
            background: "#f9fafb",
            border: "1px solid #e5e7eb",
            borderRadius: 8,
            whiteSpace: "pre-wrap",
          }}
        >
          <div>
            <strong>Trạng thái:</strong> {status}
          </div>
          {errorMsg && (
            <details style={{ marginTop: 8 }}>
              <summary>Lỗi chi tiết</summary>
              <pre style={{ fontSize: 12 }}>{errorMsg}</pre>
            </details>
          )}
        </div>
      </div>

      <div style={{ marginBottom: 16 }}>
        {isLoaded ? (
          <GoogleMap
            mapContainerStyle={mapContainerStyle}
            center={center}
            zoom={14}
            onClick={(e) => {
              const pos = {
                lat: e.latLng!.lat(),
                lng: e.latLng!.lng(),
              };
              setMarker(pos);
              setStatus(
                `Đặt marker: ${pos.lat.toFixed(6)}, ${pos.lng.toFixed(6)}`
              );
            }}
            onLoad={() => setStatus("Map loaded ✅")}
            options={{
              mapTypeControl: false,
              streetViewControl: false,
              fullscreenControl: true,
            }}
          >
            <Marker position={marker} />
          </GoogleMap>
        ) : (
          <div
            style={{
              height: "70vh",
              display: "grid",
              placeItems: "center",
              borderRadius: 12,
              border: "1px solid #e5e7eb",
            }}
          >
            {loadError ? "Lỗi tải SDK" : "Đang tải Google Maps SDK…"}
          </div>
        )}
      </div>
    </div>
  );
}
