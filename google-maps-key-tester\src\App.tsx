import { useEffect, useMemo, useRef, useState } from "react";
import {
  GoogleMap,
  Marker,
  useLoadScript,
  Autocomplete,
  DirectionsRenderer,
} from "@react-google-maps/api";

const libraries: ("places")[] = ["places"]; // cần cho Autocomplete và Directions

type LatLng = {
  lat: number;
  lng: number;
};

type RouteInfo = {
  distance: string;
  duration: string;
  steps: number;
};

export default function App() {
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY as string;
  const [center, setCenter] = useState<LatLng>({
    lat: 10.776889,
    lng: 106.700806,
  }); // HCM
  const [marker, setMarker] = useState<LatLng>(center);
  const [status, setStatus] = useState<string>("Chưa tải");
  const [errorMsg, setErrorMsg] = useState<string>("");

  // Directions states
  const [startLocation, setStartLocation] = useState<LatLng | null>(null);
  const [endLocation, setEndLocation] = useState<LatLng | null>(null);
  const [directionsResponse, setDirectionsResponse] = useState<google.maps.DirectionsResult | null>(null);
  const [routeInfo, setRouteInfo] = useState<RouteInfo | null>(null);
  const [isCalculatingRoute, setIsCalculatingRoute] = useState(false);

  // Refs
  const inputRef = useRef<HTMLInputElement | null>(null);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const startAutocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const endAutocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);

  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: apiKey,
    libraries,
  });

  useEffect(() => {
    if (loadError) {
      setStatus("Lỗi tải SDK");
      setErrorMsg(loadError.message || "Không rõ nguyên nhân");
    }
  }, [loadError]);

  useEffect(() => {
    if (isLoaded) {
      setStatus("SDK đã tải – KEY hoạt động ✅");
    }
  }, [isLoaded]);

  const mapContainerStyle = useMemo(
    () => ({
      width: "100%",
      height: "70vh",
      borderRadius: "12px",
      border: "1px solid #e5e7eb",
    }),
    []
  );

  const handlePlaceChanged = () => {
    const place = autocompleteRef.current?.getPlace();
    if (!place || !place.geometry || !place.geometry.location) {
      setStatus("Không lấy được vị trí từ Autocomplete ❌");
      return;
    }
    const pos = {
      lat: place.geometry.location.lat(),
      lng: place.geometry.location.lng(),
    };
    setCenter(pos);
    setMarker(pos);
    setStatus(`Đã chọn: ${place.formatted_address || place.name} ✅`);
  };

  const geocodeAddress = async () => {
    const addr = inputRef.current?.value?.trim();
    if (!addr) {
      setStatus("Nhập địa chỉ để geocode");
      return;
    }
    setStatus("Đang geocode…");
    try {
      const url = new URL("https://maps.googleapis.com/maps/api/geocode/json");
      url.searchParams.set("address", addr);
      url.searchParams.set("key", apiKey);

      const res = await fetch(url.toString());
      const json = await res.json();

      if (json.status !== "OK" || !json.results?.length) {
        setStatus(`Geocode thất bại: ${json.status} ❌`);
        setErrorMsg(JSON.stringify(json, null, 2));
        return;
      }

      const loc = json.results[0].geometry.location;
      const pos = { lat: loc.lat, lng: loc.lng };
      setCenter(pos);
      setMarker(pos);
      setStatus(`Geocode OK: ${json.results[0].formatted_address} ✅`);
      setErrorMsg("");
    } catch (e) {
      setStatus("Geocode lỗi ❌");
      setErrorMsg(String(e));
    }
  };

  // Directions functions
  const handleStartPlaceChanged = () => {
    const place = startAutocompleteRef.current?.getPlace();
    if (!place || !place.geometry || !place.geometry.location) {
      setStatus("Không lấy được điểm bắt đầu ❌");
      return;
    }
    const pos = {
      lat: place.geometry.location.lat(),
      lng: place.geometry.location.lng(),
    };
    setStartLocation(pos);
    setStatus(`Điểm bắt đầu: ${place.formatted_address || place.name} ✅`);
  };

  const handleEndPlaceChanged = () => {
    const place = endAutocompleteRef.current?.getPlace();
    if (!place || !place.geometry || !place.geometry.location) {
      setStatus("Không lấy được điểm kết thúc ❌");
      return;
    }
    const pos = {
      lat: place.geometry.location.lat(),
      lng: place.geometry.location.lng(),
    };
    setEndLocation(pos);
    setStatus(`Điểm kết thúc: ${place.formatted_address || place.name} ✅`);
  };

  const calculateRoute = async () => {
    if (!startLocation || !endLocation) {
      setStatus("Vui lòng chọn cả điểm bắt đầu và điểm kết thúc ❌");
      return;
    }

    if (!isLoaded) {
      setStatus("Google Maps chưa tải xong ❌");
      return;
    }

    setIsCalculatingRoute(true);
    setStatus("Đang tính toán đường đi...");

    try {
      const directionsService = new google.maps.DirectionsService();

      directionsService.route(
        {
          origin: startLocation,
          destination: endLocation,
          travelMode: google.maps.TravelMode.DRIVING,
          optimizeWaypoints: true,
          avoidHighways: false,
          avoidTolls: false,
        },
        (result, status) => {
          if (status === "OK" && result) {
            setDirectionsResponse(result);

            // Extract route information
            const route = result.routes[0];
            const leg = route.legs[0];

            setRouteInfo({
              distance: leg.distance?.text || "N/A",
              duration: leg.duration?.text || "N/A",
              steps: leg.steps?.length || 0,
            });

            setStatus(`Tìm đường thành công! Khoảng cách: ${leg.distance?.text}, Thời gian: ${leg.duration?.text} ✅`);
            setErrorMsg("");
          } else {
            setStatus(`Không thể tìm đường: ${status} ❌`);
            setErrorMsg(`Directions API error: ${status}`);
          }
          setIsCalculatingRoute(false);
        }
      );
    } catch (error) {
      setStatus("Lỗi khi tính toán đường đi ❌");
      setErrorMsg(String(error));
      setIsCalculatingRoute(false);
    }
  };

  const clearRoute = () => {
    setDirectionsResponse(null);
    setRouteInfo(null);
    setStartLocation(null);
    setEndLocation(null);
    setStatus("Đã xóa tuyến đường");
  };

  if (!apiKey) {
    return (
      <div style={{ padding: 24, fontFamily: "system-ui", maxWidth: 800, margin: "0 auto" }}>
        <h2>❌ Thiếu Google Maps API Key</h2>

        <div style={{ background: "#fef3c7", border: "1px solid #f59e0b", borderRadius: 8, padding: 16, marginBottom: 16 }}>
          <h3 style={{ margin: "0 0 12px 0", color: "#92400e" }}>🔧 Cách cấu hình API Key:</h3>
          <ol style={{ margin: 0, paddingLeft: 20 }}>
            <li>Tạo file <code>.env</code> trong thư mục gốc của dự án</li>
            <li>Thêm dòng: <code style={{ background: "#fff", padding: "2px 4px", borderRadius: 4 }}>VITE_GOOGLE_MAPS_API_KEY=YOUR_API_KEY_HERE</code></li>
            <li><strong>Lưu ý:</strong> Không có khoảng trắng xung quanh dấu =</li>
            <li>Restart server (Ctrl+C rồi npm run dev)</li>
          </ol>
        </div>

        <div style={{ background: "#dbeafe", border: "1px solid #3b82f6", borderRadius: 8, padding: 16, marginBottom: 16 }}>
          <h3 style={{ margin: "0 0 12px 0", color: "#1e40af" }}>🔑 Cách lấy Google Maps API Key:</h3>
          <ol style={{ margin: 0, paddingLeft: 20 }}>
            <li>Truy cập <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer">Google Cloud Console</a></li>
            <li>Tạo project mới hoặc chọn project hiện có</li>
            <li>Vào "APIs & Services" → "Library"</li>
            <li>Tìm và enable các API sau:
              <ul style={{ marginTop: 8 }}>
                <li>Maps JavaScript API</li>
                <li>Places API</li>
                <li>Directions API</li>
                <li>Geocoding API</li>
              </ul>
            </li>
            <li>Vào "APIs & Services" → "Credentials"</li>
            <li>Click "Create Credentials" → "API Key"</li>
            <li>Copy API key và paste vào file .env</li>
          </ol>
        </div>

        <div style={{ background: "#fee2e2", border: "1px solid #ef4444", borderRadius: 8, padding: 16 }}>
          <h3 style={{ margin: "0 0 12px 0", color: "#dc2626" }}>⚠️ Lưu ý bảo mật:</h3>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            <li>Hạn chế API key chỉ cho domain của bạn</li>
            <li>Không commit file .env lên Git</li>
            <li>Sử dụng billing alerts để tránh chi phí bất ngờ</li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div
      style={{
        padding: 24,
        fontFamily: "system-ui",
        maxWidth: 1000,
        margin: "0 auto",
      }}
    >
      <h1 style={{ marginBottom: 8 }}>Google Maps Key Tester & Route Finder</h1>
      <p style={{ marginTop: 0, color: "#6b7280" }}>
        Kiểm tra: load bản đồ (Maps JS), tìm địa điểm (Places Autocomplete), geocode địa chỉ (Geocoding), và tìm đường đi ngắn nhất.
      </p>

      {/* Route Planning Section */}
      <div style={{
        background: "#f8fafc",
        padding: 16,
        borderRadius: 12,
        border: "1px solid #e2e8f0",
        marginBottom: 16
      }}>
        <h2 style={{ margin: "0 0 12px 0", fontSize: 18, color: "#1e293b" }}>🗺️ Tìm đường đi ngắn nhất</h2>

        <div style={{ display: "grid", gap: 12, marginBottom: 12 }}>
          <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 12 }}>
            <div>
              <label style={{ display: "block", marginBottom: 6, fontWeight: 600, fontSize: 14 }}>
                📍 Điểm bắt đầu
              </label>
              {isLoaded && (
                <Autocomplete
                  onLoad={(ac) => (startAutocompleteRef.current = ac)}
                  onPlaceChanged={handleStartPlaceChanged}
                >
                  <input
                    type="text"
                    placeholder="Nhập điểm bắt đầu..."
                    style={{
                      width: "100%",
                      padding: "10px 12px",
                      borderRadius: 8,
                      border: "1px solid #d1d5db",
                      outline: "none",
                      fontSize: 14,
                    }}
                  />
                </Autocomplete>
              )}
            </div>

            <div>
              <label style={{ display: "block", marginBottom: 6, fontWeight: 600, fontSize: 14 }}>
                🎯 Điểm kết thúc
              </label>
              {isLoaded && (
                <Autocomplete
                  onLoad={(ac) => (endAutocompleteRef.current = ac)}
                  onPlaceChanged={handleEndPlaceChanged}
                >
                  <input
                    type="text"
                    placeholder="Nhập điểm kết thúc..."
                    style={{
                      width: "100%",
                      padding: "10px 12px",
                      borderRadius: 8,
                      border: "1px solid #d1d5db",
                      outline: "none",
                      fontSize: 14,
                    }}
                  />
                </Autocomplete>
              )}
            </div>
          </div>

          <div style={{ display: "flex", gap: 8 }}>
            <button
              onClick={calculateRoute}
              disabled={isCalculatingRoute || !startLocation || !endLocation}
              style={{
                padding: "10px 16px",
                borderRadius: 8,
                border: "1px solid #059669",
                background: isCalculatingRoute || !startLocation || !endLocation ? "#9ca3af" : "#059669",
                color: "white",
                cursor: isCalculatingRoute || !startLocation || !endLocation ? "not-allowed" : "pointer",
                fontSize: 14,
                fontWeight: 600,
              }}
            >
              {isCalculatingRoute ? "⏳ Đang tính..." : "🚗 Tìm đường đi"}
            </button>

            <button
              onClick={clearRoute}
              disabled={!directionsResponse}
              style={{
                padding: "10px 16px",
                borderRadius: 8,
                border: "1px solid #dc2626",
                background: !directionsResponse ? "#9ca3af" : "#dc2626",
                color: "white",
                cursor: !directionsResponse ? "not-allowed" : "pointer",
                fontSize: 14,
                fontWeight: 600,
              }}
            >
              🗑️ Xóa tuyến đường
            </button>
          </div>

          {/* Route Information */}
          {routeInfo && (
            <div style={{
              background: "#ecfdf5",
              border: "1px solid #10b981",
              borderRadius: 8,
              padding: 12,
            }}>
              <h3 style={{ margin: "0 0 8px 0", fontSize: 16, color: "#065f46" }}>📊 Thông tin tuyến đường</h3>
              <div style={{ display: "grid", gridTemplateColumns: "repeat(3, 1fr)", gap: 12, fontSize: 14 }}>
                <div>
                  <strong>📏 Khoảng cách:</strong><br />
                  <span style={{ color: "#059669" }}>{routeInfo.distance}</span>
                </div>
                <div>
                  <strong>⏱️ Thời gian:</strong><br />
                  <span style={{ color: "#059669" }}>{routeInfo.duration}</span>
                </div>
                <div>
                  <strong>🔢 Số bước:</strong><br />
                  <span style={{ color: "#059669" }}>{routeInfo.steps} bước</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Original Testing Section */}
      <div style={{ display: "grid", gap: 12, marginBottom: 12 }}>
        <div>
          <label style={{ display: "block", marginBottom: 6, fontWeight: 600 }}>
            Tìm nơi (Autocomplete – Places API)
          </label>
          {isLoaded && (
            <Autocomplete
              onLoad={(ac) => (autocompleteRef.current = ac)}
              onPlaceChanged={handlePlaceChanged}
            >
              <input
                type="text"
                placeholder="Ví dụ: Landmark 81, Saigon Zoo…"
                style={{
                  width: "100%",
                  padding: "10px 12px",
                  borderRadius: 8,
                  border: "1px solid #d1d5db",
                  outline: "none",
                }}
                ref={inputRef}
              />
            </Autocomplete>
          )}
        </div>

        <div style={{ display: "flex", gap: 8 }}>
          <button
            onClick={geocodeAddress}
            style={{
              padding: "10px 14px",
              borderRadius: 8,
              border: "1px solid #2563eb",
              background: "#2563eb",
              color: "white",
              cursor: "pointer",
            }}
          >
            Geocode địa chỉ (REST)
          </button>
          <button
            onClick={() => {
              const hcm = { lat: 10.776889, lng: 106.700806 };
              setCenter(hcm);
              setMarker(hcm);
              setStatus("Reset về HCM");
            }}
            style={{
              padding: "10px 14px",
              borderRadius: 8,
              border: "1px solid #e5e7eb",
              background: "white",
              cursor: "pointer",
            }}
          >
            Reset về HCM
          </button>
        </div>

        <div
          style={{
            padding: 12,
            background: "#f9fafb",
            border: "1px solid #e5e7eb",
            borderRadius: 8,
            whiteSpace: "pre-wrap",
          }}
        >
          <div>
            <strong>Trạng thái:</strong> {status}
          </div>
          <div style={{ marginTop: 8, fontSize: 12, color: "#6b7280" }}>
            <strong>API Key:</strong> {apiKey ? `${apiKey.substring(0, 20)}...` : "Không có"}
          </div>
          {errorMsg && (
            <details style={{ marginTop: 8 }}>
              <summary>Lỗi chi tiết</summary>
              <pre style={{ fontSize: 12 }}>{errorMsg}</pre>
            </details>
          )}
        </div>
      </div>

      <div style={{ marginBottom: 16 }}>
        {isLoaded ? (
          <GoogleMap
            mapContainerStyle={mapContainerStyle}
            center={center}
            zoom={directionsResponse ? 10 : 14}
            onClick={(e) => {
              if (!directionsResponse) {
                const pos = {
                  lat: e.latLng!.lat(),
                  lng: e.latLng!.lng(),
                };
                setMarker(pos);
                setStatus(
                  `Đặt marker: ${pos.lat.toFixed(6)}, ${pos.lng.toFixed(6)}`
                );
              }
            }}
            onLoad={(map) => {
              setStatus("Map loaded ✅");
              // Fit bounds to route if available
              if (directionsResponse && directionsResponse.routes[0].bounds) {
                map.fitBounds(directionsResponse.routes[0].bounds);
              }
            }}
            options={{
              mapTypeControl: false,
              streetViewControl: false,
              fullscreenControl: true,
            }}
          >
            {/* Show directions if available */}
            {directionsResponse && (
              <DirectionsRenderer
                directions={directionsResponse}
                options={{
                  suppressMarkers: false,
                  polylineOptions: {
                    strokeColor: "#2563eb",
                    strokeWeight: 5,
                    strokeOpacity: 0.8,
                  },
                }}
              />
            )}

            {/* Show regular marker only if no directions */}
            {!directionsResponse && <Marker position={marker} />}

            {/* Show start and end markers when locations are selected but no route calculated */}
            {!directionsResponse && startLocation && (
              <Marker
                position={startLocation}
                icon={{
                  url: "data:image/svg+xml;charset=UTF-8," + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="16" cy="16" r="12" fill="#059669" stroke="white" stroke-width="2"/>
                      <text x="16" y="20" text-anchor="middle" fill="white" font-size="16" font-weight="bold">S</text>
                    </svg>
                  `),
                  scaledSize: new google.maps.Size(32, 32),
                }}
              />
            )}

            {!directionsResponse && endLocation && (
              <Marker
                position={endLocation}
                icon={{
                  url: "data:image/svg+xml;charset=UTF-8," + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="16" cy="16" r="12" fill="#dc2626" stroke="white" stroke-width="2"/>
                      <text x="16" y="20" text-anchor="middle" fill="white" font-size="16" font-weight="bold">E</text>
                    </svg>
                  `),
                  scaledSize: new google.maps.Size(32, 32),
                }}
              />
            )}
          </GoogleMap>
        ) : (
          <div
            style={{
              height: "70vh",
              display: "grid",
              placeItems: "center",
              borderRadius: 12,
              border: "1px solid #e5e7eb",
            }}
          >
            {loadError ? "Lỗi tải SDK" : "Đang tải Google Maps SDK…"}
          </div>
        )}
      </div>
    </div>
  );
}
